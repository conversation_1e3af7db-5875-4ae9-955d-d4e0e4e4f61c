"""
Tests for document processing error logging.

This module contains unit tests for the document processing error logging functionality.
"""

import pytest
from unittest.mock import MagicMock, patch, AsyncMock
import uuid
import json
from fastapi import HTTPException
from sqlalchemy.orm import Session

from app.routers.documents import analyze_file_content, analyze_url
from app.repositories.document_processing_log_repository import create_document_processing_log


@pytest.mark.asyncio
@patch('app.routers.documents.process_document')
@patch('app.routers.documents.get_latest_llm_configuration')
@patch('app.routers.documents.upload_file')
@patch('app.routers.documents.create_document_processing_log')
async def test_analyze_file_content_error_logging(
    mock_create_log, mock_upload_file, mock_get_llm_config, mock_process_document
):
    """Test that errors during file content analysis are logged to the database."""
    # Mock the LLM configuration
    mock_llm_config = MagicMock()
    mock_llm_config.id = 1
    mock_get_llm_config.return_value = mock_llm_config

    # Mock the upload_file function to return a GS URI
    mock_upload_file.return_value = ("gs://bucket/documents/test.pdf", "https://example.com/test.pdf")

    # Mock the process_document function to raise an exception
    mock_process_document.side_effect = AsyncMock(side_effect=Exception("Test error"))

    # Mock the create_document_processing_log function
    mock_log = MagicMock()
    mock_log.id = uuid.uuid4()
    mock_create_log.return_value = mock_log

    # Mock database session
    mock_db = MagicMock(spec=Session)

    # Call the function and expect an HTTPException
    with pytest.raises(HTTPException) as excinfo:
        await analyze_file_content(
            file_content=b"test content",
            filename="test.pdf",
            document_type="standard_invoice",
            vendor_name="Test Vendor",
            invoice_amount=100.0,
            db=mock_db
        )

    # Assert the exception details
    assert excinfo.value.status_code == 500
    assert "Error analyzing document" in excinfo.value.detail

    # Assert that create_document_processing_log was called with error status
    mock_create_log.assert_called_once()
    call_args = mock_create_log.call_args[1]
    assert call_args["status"] == "error"
    assert call_args["extracted_data"] == {}
    assert "error" in call_args["raw_llm_response"]
    assert "Test error" in call_args["raw_llm_response"]["error"]
    assert call_args["llm_configuration_id"] == 1


@pytest.mark.asyncio
@patch('app.routers.documents.process_document_from_url')
@patch('app.routers.documents.get_latest_llm_configuration')
@patch('app.routers.documents.create_document_processing_log')
async def test_analyze_url_error_logging(
    mock_create_log, mock_get_llm_config, mock_process_document_from_url
):
    """Test that errors during URL document analysis are logged to the database."""
    # Mock the LLM configuration
    mock_llm_config = MagicMock()
    mock_llm_config.id = 1
    mock_get_llm_config.return_value = mock_llm_config

    # Mock the process_document_from_url function to raise an exception
    mock_process_document_from_url.side_effect = AsyncMock(side_effect=Exception("URL processing error"))

    # Mock the create_document_processing_log function
    mock_log = MagicMock()
    mock_log.id = uuid.uuid4()
    mock_create_log.return_value = mock_log

    # Mock database session
    mock_db = MagicMock(spec=Session)

    # Call the function and expect an HTTPException
    with pytest.raises(HTTPException) as excinfo:
        await analyze_url(
            file_url="https://example.com/test.pdf",
            document_type="standard_invoice",
            vendor_name="Test Vendor",
            invoice_amount=100.0,
            db=mock_db
        )

    # Assert the exception details
    assert excinfo.value.status_code == 500
    assert "Error analyzing document from URL" in excinfo.value.detail

    # Assert that create_document_processing_log was called with error status
    mock_create_log.assert_called_once()
    call_args = mock_create_log.call_args[1]
    assert call_args["status"] == "error"
    assert call_args["extracted_data"] == {}
    assert "error" in call_args["raw_llm_response"]
    assert "URL processing error" in call_args["raw_llm_response"]["error"]
    assert call_args["llm_configuration_id"] == 1
