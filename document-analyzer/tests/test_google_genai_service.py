"""
Tests for the Google Generative AI Service.

This module contains unit tests for the Google Generative AI service.
"""

import os
import pytest
from unittest.mock import patch, MagicMock, AsyncMock
from fastapi import UploadFile, HTTPException
from sqlalchemy.orm import Session

from app.services.google_genai_service import (
    get_latest_llm_configuration,
    configure_genai_client,
    process_document,
    calculate_summary
)
from app.models.database_models import LLMConfiguration


# Tests for calculate_summary function
def test_calculate_summary_with_fields():
    """Test calculating summary with fields."""
    # Create test fields
    fields = {
        "vendor_name": {
            "input_value": "Test Vendor",
            "ocr_value": "Test Vendor",
            "status": "matched"
        },
        "invoice_number": {
            "input_value": "INV-123",
            "ocr_value": "INV-123",
            "status": "matched"
        },
        "invoice_date": {
            "input_value": "2023-01-01",
            "ocr_value": "2023-01-02",
            "status": "mismatched"
        },
        "invoice_amount": {
            "input_value": "100.00",
            "ocr_value": "",
            "status": "not_found"
        },
        "vat_amount": {
            "input_value": "10.00",
            "ocr_value": "10.00",
            "status": "matched"
        }
    }

    # Call the function
    summary = calculate_summary(fields)

    # Assert the summary values
    assert summary["total_fields"] == 5
    assert summary["matched"] == 3
    assert summary["mismatched"] == 1
    assert summary["not_found"] == 1


def test_calculate_summary_empty_fields():
    """Test calculating summary with empty fields."""
    # Call the function with empty fields
    summary = calculate_summary({})

    # Assert the summary values
    assert summary["total_fields"] == 0
    assert summary["matched"] == 0
    assert summary["mismatched"] == 0
    assert summary["not_found"] == 0


def test_calculate_summary_missing_status():
    """Test calculating summary with fields missing status."""
    # Create test fields with missing status
    fields = {
        "vendor_name": {
            "input_value": "Test Vendor",
            "ocr_value": "Test Vendor",
            "status": "matched"
        },
        "invoice_number": {
            "input_value": "INV-123",
            "ocr_value": "INV-123"
            # Missing status
        }
    }

    # Call the function
    summary = calculate_summary(fields)

    # Assert the summary values
    assert summary["total_fields"] == 2
    assert summary["matched"] == 1
    assert summary["mismatched"] == 0
    assert summary["not_found"] == 0


# Tests for get_latest_llm_configuration function
def test_get_latest_llm_configuration():
    """Test getting the latest LLM configuration for a specific document type."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Create a mock configuration
    config = MagicMock(spec=LLMConfiguration)

    # Mock repository function to return the configuration
    with patch('app.repositories.llm_configuration_repository.get_latest_llm_configuration_by_document_type') as mock_get_latest:
        mock_get_latest.return_value = config

        # Call the function with default document_type
        result = get_latest_llm_configuration(db)

        # Assert the result is the configuration
        assert result == config

        # Assert the repository function was called with the correct document_type
        mock_get_latest.assert_called_once_with(db, "standard_invoice")


def test_get_latest_llm_configuration_with_document_type():
    """Test getting the latest LLM configuration for a specific document type."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Create a mock configuration
    config = MagicMock(spec=LLMConfiguration)

    # Mock repository function to return the configuration
    with patch('app.repositories.llm_configuration_repository.get_latest_llm_configuration_by_document_type') as mock_get_latest:
        mock_get_latest.return_value = config

        # Call the function with specific document_type
        result = get_latest_llm_configuration(db, "tax_invoice")

        # Assert the result is the configuration
        assert result == config

        # Assert the repository function was called with the correct document_type
        mock_get_latest.assert_called_once_with(db, "tax_invoice")


def test_get_latest_llm_configuration_none():
    """Test getting the latest LLM configuration when none exists."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Mock repository function to return None
    with patch('app.repositories.llm_configuration_repository.get_latest_llm_configuration_by_document_type') as mock_get_latest:
        mock_get_latest.return_value = None

        # Call the function
        result = get_latest_llm_configuration(db)

        # Assert the result is None
        assert result is None

        # Assert the repository function was called
        mock_get_latest.assert_called_once_with(db, "standard_invoice")


# Tests for process_document function
@patch('app.services.google_genai_service.get_latest_llm_configuration')
@pytest.mark.asyncio
async def test_process_document_no_config(mock_get_config):
    """Test processing a document when no LLM configuration exists."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Mock file
    file = AsyncMock(spec=UploadFile)
    file.filename = "test.pdf"

    # Mock get_latest_llm_configuration to return None
    mock_get_config.return_value = None

    # Call the function and expect an HTTPException
    with pytest.raises(HTTPException) as excinfo:
        await process_document(gs_uri="gs://bucket/test.pdf", user_prompt="Test prompt", db=db, document_type="standard_invoice")

    # Assert the exception details
    assert excinfo.value.status_code == 400
    assert "No LLM configuration found for document type: standard_invoice" in str(excinfo.value.detail)

    # Assert the mock functions were called
    mock_get_config.assert_called_once_with(db, "standard_invoice")


@patch('app.services.google_genai_service.get_latest_llm_configuration')
@pytest.mark.asyncio
async def test_process_document_invalid_provider(mock_get_config):
    """Test processing a document with an invalid provider."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Mock configuration with invalid provider
    config = MagicMock(spec=LLMConfiguration)
    config.provider = "openai"  # Not in ["google", "vertex-ai"]

    # Mock get_latest_llm_configuration to return the configuration
    mock_get_config.return_value = config

    # Call the function and expect an HTTPException
    with pytest.raises(HTTPException) as excinfo:
        await process_document(gs_uri="gs://bucket/test.pdf", user_prompt="Test prompt", db=db, document_type="tax_invoice")

    # Assert the exception details
    assert "Invalid provider" in str(excinfo.value.detail)
    assert "configured providers" in str(excinfo.value.detail)

    # Assert the mock functions were called
    mock_get_config.assert_called_once_with(db, "tax_invoice")


@patch('app.services.google_genai_service.get_latest_llm_configuration')
@patch('app.services.google_genai_service.configure_genai_client')
@pytest.mark.asyncio
async def test_process_document_standard_invoice(mock_configure_client, mock_get_config):
    """Test successful document processing for standard invoice."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Mock configuration
    config = MagicMock(spec=LLMConfiguration)
    config.provider = "google"
    config.model_id = "gemini-pro-vision"
    config.temperature = 0.2
    config.top_p = 0.95
    config.top_k = 40
    config.system_instruction = "Extract invoice data"

    # Mock get_latest_llm_configuration to return the configuration
    mock_get_config.return_value = config

    # Mock the client and its response
    mock_client = MagicMock()
    mock_model = MagicMock()
    mock_client.models = mock_model

    # Mock the response
    mock_response = MagicMock()
    mock_candidate = MagicMock()
    mock_content = MagicMock()
    mock_part = MagicMock()
    mock_part.text = '''
    {
        "document_type": "standard_invoice",
        "page_count": 1,
        "results": {
            "fields": {
                "vendor_name": {
                    "input_value": "Test Vendor",
                    "ocr_value": "Test Vendor",
                    "status": "matched"
                },
                "invoice_number": {
                    "input_value": "INV-123",
                    "ocr_value": "INV-123",
                    "status": "matched"
                },
                "invoice_date": {
                    "input_value": "2024-10-18",
                    "ocr_value": "2024-10-18",
                    "status": "matched"
                },
                "invoice_amount": {
                    "input_value": "100.00",
                    "ocr_value": "100.00",
                    "status": "matched"
                },
                "vat_amount": {
                    "input_value": "10.00",
                    "ocr_value": "10.00",
                    "status": "matched"
                }
            }
        }
    }
    '''

    mock_content.parts = [mock_part]
    mock_candidate.content = mock_content
    mock_response.candidates = [mock_candidate]

    mock_model.generate_content.return_value = mock_response
    mock_configure_client.return_value = mock_client

    # Call the function with standard_invoice document type
    result = await process_document(
        gs_uri="gs://bucket/test.pdf",
        user_prompt="Test prompt",
        db=db,
        document_type="standard_invoice"
    )

    # Assert the result contains both extracted data and raw response
    assert "extracted_data" in result
    assert "raw_response" in result

    # Check extracted data
    assert result["extracted_data"]["document_type"] == "standard_invoice"
    assert result["extracted_data"]["page_count"] == 1

    # Check fields
    fields = result["extracted_data"]["results"]["fields"]
    assert fields["vendor_name"]["input_value"] == "Test Vendor"
    assert fields["invoice_number"]["input_value"] == "INV-123"
    assert fields["invoice_date"]["input_value"] == "2024-10-18"

    # Check that summary was calculated correctly
    summary = result["extracted_data"]["results"]["summary"]
    assert summary["total_fields"] == 5
    assert summary["matched"] == 5
    assert summary["mismatched"] == 0
    assert summary["not_found"] == 0

    # Check raw response is included and has the expected structure
    assert "candidates" in result["raw_response"]
    assert len(result["raw_response"]["candidates"]) > 0
    assert "content" in result["raw_response"]["candidates"][0]
    assert "parts" in result["raw_response"]["candidates"][0]["content"]

    # Verify the text content was properly extracted
    assert any(
        "text" in part and "document_type" in part["text"] and "standard_invoice" in part["text"]
        for part in result["raw_response"]["candidates"][0]["content"]["parts"]
    )

    # Assert the mocks were called correctly
    mock_get_config.assert_called_once_with(db, "standard_invoice")
    mock_configure_client.assert_called_once()
    mock_model.generate_content.assert_called_once()


@patch('app.services.google_genai_service.get_latest_llm_configuration')
@patch('app.services.google_genai_service.configure_genai_client')
@pytest.mark.asyncio
async def test_process_document_tax_invoice(mock_configure_client, mock_get_config):
    """Test successful document processing for tax invoice."""
    # Mock database session
    db = MagicMock(spec=Session)

    # Mock configuration
    config = MagicMock(spec=LLMConfiguration)
    config.provider = "google"
    config.model_id = "gemini-pro-vision"
    config.temperature = 0.2
    config.top_p = 0.95
    config.top_k = 40
    config.system_instruction = "Extract tax invoice data"

    # Mock get_latest_llm_configuration to return the configuration
    mock_get_config.return_value = config

    # Mock the client and its response
    mock_client = MagicMock()
    mock_model = MagicMock()
    mock_client.models = mock_model

    # Mock the response
    mock_response = MagicMock()
    mock_candidate = MagicMock()
    mock_content = MagicMock()
    mock_part = MagicMock()
    mock_part.text = '''
    {
        "document_type": "tax_invoice",
        "page_count": 1,
        "results": {
            "fields": {
                "vendor_name": {
                    "input_value": "Test Vendor",
                    "ocr_value": "Test Vendor",
                    "status": "matched"
                },
                "tax_invoice_number": {
                    "input_value": "TAX-123",
                    "ocr_value": "TAX-123",
                    "status": "matched"
                },
                "tax_invoice_date": {
                    "input_value": "2024-10-18",
                    "ocr_value": "2024-10-18",
                    "status": "matched"
                },
                "invoice_amount": {
                    "input_value": "100.00",
                    "ocr_value": "100.00",
                    "status": "matched"
                },
                "vat_amount": {
                    "input_value": "10.00",
                    "ocr_value": "10.00",
                    "status": "matched"
                }
            }
        }
    }
    '''

    mock_content.parts = [mock_part]
    mock_candidate.content = mock_content
    mock_response.candidates = [mock_candidate]

    mock_model.generate_content.return_value = mock_response
    mock_configure_client.return_value = mock_client

    # Call the function with tax_invoice document type
    result = await process_document(
        gs_uri="gs://bucket/test.pdf",
        user_prompt="Test prompt",
        db=db,
        document_type="tax_invoice"
    )

    # Assert the result contains both extracted data and raw response
    assert "extracted_data" in result
    assert "raw_response" in result

    # Check extracted data
    assert result["extracted_data"]["document_type"] == "tax_invoice"
    assert result["extracted_data"]["page_count"] == 1

    # Check fields
    fields = result["extracted_data"]["results"]["fields"]
    assert fields["vendor_name"]["input_value"] == "Test Vendor"
    assert fields["tax_invoice_number"]["input_value"] == "TAX-123"
    assert fields["tax_invoice_date"]["input_value"] == "2024-10-18"

    # Check that summary was calculated correctly
    summary = result["extracted_data"]["results"]["summary"]
    assert summary["total_fields"] == 5
    assert summary["matched"] == 5
    assert summary["mismatched"] == 0
    assert summary["not_found"] == 0

    # Check raw response is included and has the expected structure
    assert "candidates" in result["raw_response"]
    assert len(result["raw_response"]["candidates"]) > 0
    assert "content" in result["raw_response"]["candidates"][0]
    assert "parts" in result["raw_response"]["candidates"][0]["content"]

    # Verify the text content was properly extracted
    assert any(
        "text" in part and "document_type" in part["text"] and "tax_invoice" in part["text"]
        for part in result["raw_response"]["candidates"][0]["content"]["parts"]
    )

    # Assert the mocks were called correctly
    mock_get_config.assert_called_once_with(db, "tax_invoice")
    mock_configure_client.assert_called_once()
    mock_model.generate_content.assert_called_once()


# Tests for configure_genai_client function
@patch('os.getenv')
def test_configure_genai_client_standard(mock_getenv):
    """Test configuring the client with standard Google AI."""
    # Mock environment variables
    mock_getenv.side_effect = lambda key, default=None: {
        "GOOGLE_GENAI_USE_VERTEXAI": "False",
        "GOOGLE_API_KEY": "test_api_key"
    }.get(key, default)

    # Mock the Client constructor
    with patch('google.genai.Client') as mock_client:
        # Call the function
        client = configure_genai_client()

        # Assert the client was created with the correct parameters
        mock_client.assert_called_once()
        assert mock_client.call_args[1]['api_key'] == "test_api_key"


@patch('os.getenv')
def test_configure_genai_client_vertex(mock_getenv):
    """Test configuring the client with Vertex AI."""
    # Mock environment variables
    mock_getenv.side_effect = lambda key, default=None: {
        "GOOGLE_GENAI_USE_VERTEXAI": "true",
        "GOOGLE_CLOUD_PROJECT": "test_project",
        "GOOGLE_CLOUD_LOCATION": "us-central1"
    }.get(key, default)

    # Mock the Client constructor
    with patch('google.genai.Client') as mock_client:
        # Call the function
        client = configure_genai_client()

        # Assert the client was created with the correct parameters
        mock_client.assert_called_once()
        # No api_key parameter should be passed
        assert 'api_key' not in mock_client.call_args[1]


@patch('os.getenv')
def test_configure_genai_client_vertex_missing_project(mock_getenv):
    """Test configuring the client with Vertex AI but missing project ID."""
    # Mock environment variables
    mock_getenv.side_effect = lambda key, default=None: {
        "GOOGLE_GENAI_USE_VERTEXAI": "true",
        "GOOGLE_CLOUD_PROJECT": None,
        "GOOGLE_CLOUD_LOCATION": "us-central1"
    }.get(key, default)

    # Call the function and expect an HTTPException
    with pytest.raises(HTTPException) as excinfo:
        configure_genai_client()

    # Assert the exception details
    assert excinfo.value.status_code == 500
    assert "Cloud project ID environment variable is required" in str(excinfo.value.detail)


@patch('os.getenv')
def test_configure_genai_client_standard_missing_api_key(mock_getenv):
    """Test configuring the client with standard Google AI but missing API key."""
    # Mock environment variables
    mock_getenv.side_effect = lambda key, default=None: {
        "GOOGLE_GENAI_USE_VERTEXAI": "False",
        "GOOGLE_API_KEY": None
    }.get(key, default)

    # Call the function and expect an HTTPException
    with pytest.raises(HTTPException) as excinfo:
        configure_genai_client()

    # Assert the exception details
    assert excinfo.value.status_code == 500
    assert "API key not found" in str(excinfo.value.detail)
