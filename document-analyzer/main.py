import logging
import uvicorn
from fastapi import Fast<PERSON><PERSON>, Security, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.models import SecurityScheme
from fastapi.security import APIKeyHeader
from app.routers import documents, llm_configurations, api_keys
from app.database import engine, Base
from app.utils.env_loader import load_env_file
from app.config import settings
from app.middleware.api_key_auth import APIKeyMiddleware, X_API_KEY, get_api_key_from_header


# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables from .env file if it exists
load_env_file()

# Define API key security scheme for OpenAPI (Swagger UI)
app = FastAPI(
    title="Document Analyzer API",
    description="API for analyzing documents",
    version="0.1.0",
    swagger_ui_init_oauth={},
    swagger_ui_parameters={"persistAuthorization": True}
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Add API key middleware (runtime security)
app.add_middleware(
    APIKeyMiddleware,
    exclude_paths=["/", "/docs", "/redoc", "/openapi.json", "/health", "/config"],
    exclude_methods=["OPTIONS"]
)

@app.get("/", dependencies=[])
async def root():
    logger.info("Root endpoint accessed")
    return {"message": "Welcome to the Document Analyzer API"}

@app.get("/health", dependencies=[])
async def health_check():
    return {"status": "healthy"}

@app.get("/config", dependencies=[])
async def get_config():
    """Get the current environment configuration (safe values only)."""
    return {
        "environment": settings.environment,
        "database": {
            "host": settings.db.host,
            "port": settings.db.port,
            "database": settings.db.database,
            "username": settings.db.username,
            # Don't include password for security reasons
        }
    }

# Create database tables
try:
    Base.metadata.create_all(bind=engine)
    print("Database tables created successfully")
except Exception as e:
    print(f"Error creating database tables: {e}")

# Protected routers
app.include_router(documents.router, dependencies=[Depends(get_api_key_from_header)])
app.include_router(llm_configurations.router, dependencies=[Depends(get_api_key_from_header)])
app.include_router(api_keys.router, dependencies=[Depends(get_api_key_from_header)])

if __name__ == "__main__":
    logger.info("Starting Document Analyzer API server")
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
