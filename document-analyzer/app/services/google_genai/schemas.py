"""
Schema definitions for Google Generative AI service.

This module provides schema definitions for extracting structured data
from documents using Google's Generative AI.
"""

from typing import Dict, List
from google.genai import types


class InvoiceSchema:
    """Collection of schemas for invoice data extraction."""

    @staticmethod
    def _create_field_schema(field_name: str) -> types.Schema:
        """
        Create a schema for a field with input_value, ocr_value, ocr_value_normalized, and status.

        Args:
            field_name: The name of the field

        Returns:
            types.Schema: Schema for the field
        """
        return types.Schema(
            type=types.Type.OBJECT,
            properties={
                "input_value": types.Schema(
                    type=types.Type.STRING,
                    description=f"The input value for {field_name}"
                ),
                "ocr_value": types.Schema(
                    type=types.Type.STRING,
                    description=f"The OCR extracted value for {field_name}"
                ),
                "ocr_value_normalized": types.Schema(
                    type=types.Type.STRING,
                    description=f"The normalized OCR extracted value for {field_name}"
                ),
                "status": types.Schema(
                    type=types.Type.STRING,
                    description="The matching status (matched, mismatched, or not_found)"
                )
            },
            required=["input_value", "ocr_value", "ocr_value_normalized", "status"]
        )


    @staticmethod
    def _create_fields_schema(include_invoice_fields: bool = False, include_tax_invoice_fields: bool = False) -> types.Schema:
        """
        Create a schema for the fields section.

        Args:
            include_invoice_fields: Whether to include standard invoice specific fields
            include_tax_invoice_fields: Whether to include tax invoice specific fields

        Returns:
            types.Schema: Schema for the fields section
        """
        properties = {
            "vendor_name": InvoiceSchema._create_field_schema("vendor name"),
            "invoice_amount": InvoiceSchema._create_field_schema("invoice amount"),
            "vat_amount": InvoiceSchema._create_field_schema("VAT amount")
        }

        # Add standard invoice specific fields if requested
        if include_invoice_fields:
            properties.update({
                "invoice_number": InvoiceSchema._create_field_schema("invoice number"),
                "invoice_date": InvoiceSchema._create_field_schema("invoice date")
            })

        # Add tax invoice specific fields if requested
        if include_tax_invoice_fields:
            properties.update({
                "tax_invoice_number": InvoiceSchema._create_field_schema("tax invoice number"),
                "tax_invoice_date": InvoiceSchema._create_field_schema("tax invoice date")
            })

        return types.Schema(
            type=types.Type.OBJECT,
            properties=properties,
            required=list(properties.keys())
        )

    @staticmethod
    def _create_results_schema(include_invoice_fields: bool = False, include_tax_invoice_fields: bool = False) -> types.Schema:
        """
        Create a schema for the results section.

        Args:
            include_invoice_fields: Whether to include standard invoice specific fields
            include_tax_invoice_fields: Whether to include tax invoice specific fields

        Returns:
            types.Schema: Schema for the results section
        """
        return types.Schema(
            type=types.Type.OBJECT,
            properties={
                "fields": InvoiceSchema._create_fields_schema(
                    include_invoice_fields=include_invoice_fields,
                    include_tax_invoice_fields=include_tax_invoice_fields
                )
            },
            required=["fields"]
        )

    @staticmethod
    def _create_base_schema(document_type: str, include_invoice_fields: bool = False, include_tax_invoice_fields: bool = False) -> types.Schema:
        """
        Create a base schema for invoice extraction.

        Args:
            document_type: The type of document (standard_invoice or tax_invoice)
            include_invoice_fields: Whether to include standard invoice specific fields
            include_tax_invoice_fields: Whether to include tax invoice specific fields

        Returns:
            types.Schema: Base schema for invoice extraction
        """
        return types.Schema(
            type=types.Type.OBJECT,
            properties={
                "document_type": types.Schema(
                    type=types.Type.STRING,
                    description=f"The document type ({document_type})"
                ),
                "page_count": types.Schema(
                    type=types.Type.INTEGER,
                    description="The number of pages in the document"
                ),
                "results": InvoiceSchema._create_results_schema(
                    include_invoice_fields=include_invoice_fields,
                    include_tax_invoice_fields=include_tax_invoice_fields
                )
            },
            required=["document_type", "page_count", "results"]
        )

    @staticmethod
    def standard_invoice_schema():
        """
        Schema for extracting standard invoice information.

        Returns:
            types.Schema: Schema for standard invoice data extraction
        """
        return InvoiceSchema._create_base_schema(
            document_type="standard_invoice",
            include_invoice_fields=True
        )

    @staticmethod
    def tax_invoice_schema():
        """
        Schema for extracting tax invoice information.

        Returns:
            types.Schema: Schema for tax invoice data extraction
        """
        return InvoiceSchema._create_base_schema(
            document_type="tax_invoice",
            include_tax_invoice_fields=True
        )

    # Legacy methods for backward compatibility
    @staticmethod
    def invoice_schema():
        """
        Basic schema for extracting essential invoice information.

        This is a legacy method that combines both standard and tax invoice fields.

        Returns:
            types.Schema: Schema for basic invoice data extraction
        """
        return InvoiceSchema._create_base_schema(
            document_type="invoice",
            include_invoice_fields=True,
            include_tax_invoice_fields=True
        )

    # For backward compatibility, we'll keep these methods but they now use the same schema
    # as the non-confidence versions since the new schema already includes validation fields
    @staticmethod
    def standard_invoice_with_confidence_schema():
        """
        Schema for extracting standard invoice information with validation.

        Returns:
            types.Schema: Schema for standard invoice data extraction with validation
        """
        return InvoiceSchema.standard_invoice_schema()

    @staticmethod
    def tax_invoice_with_confidence_schema():
        """
        Schema for extracting tax invoice information with validation.

        Returns:
            types.Schema: Schema for tax invoice data extraction with validation
        """
        return InvoiceSchema.tax_invoice_schema()

    @staticmethod
    def invoice_with_confidence_schema():
        """
        Schema for extracting invoice information with validation.

        This is a legacy method that combines both standard and tax invoice fields.

        Returns:
            types.Schema: Schema for invoice data extraction with validation
        """
        return InvoiceSchema.invoice_schema()
