# Environment
APP_ENVIRONMENT=development  # Options: development, staging, production

# Database settings
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_HOST=localhost
DB_PORT=5432
DB_DATABASE=document_analyzer

# Google Generative AI configuration
# For standard Google AI API (default)
# GOOGLE_API_KEY=your_google_api_key

# For Vertex AI integration
# Set to "True" to use Vertex AI instead of standard Google AI API
GOOGLE_GENAI_USE_VERTEXAI=True
# Your Google Cloud project ID
GOOGLE_CLOUD_PROJECT=gen-lang-client-0286440102
# Google Cloud region (default: us-central1)
GOOGLE_CLOUD_LOCATION=us-central1
# Google Cloud Storage bucket name
GOOGLE_CLOUD_STORAGE_BUCKET=your_bucket_name

# For staging
# APP_ENVIRONMENT=staging
# DB_USERNAME=postgres
# DB_PASSWORD=your_password
# DB_HOST=staging-db-host
# DB_PORT=5432
# DB_DATABASE=document_analyzer_staging

# For production
# APP_ENVIRONMENT=production
# DB_USERNAME=postgres
# DB_PASSWORD=your_secure_password
# DB_HOST=production-db-host
# DB_PORT=5432
# DB_DATABASE=document_analyzer_prod
