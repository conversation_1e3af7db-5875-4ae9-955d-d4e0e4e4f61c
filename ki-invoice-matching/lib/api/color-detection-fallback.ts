/**
 * Fallback implementation of PDF color detection
 * This implementation doesn't rely on canvas and provides a simpler approach
 * that can be used when the canvas library is not available
 */

import * as pdfjsLib from 'pdfjs-dist';
import path from 'path';

// Define the color detection result interface
export interface ColorDetectionResult {
  is_colored: boolean;
  color_pages: number[];
  total_pages: number;
}

/**
 * A simplified color detection implementation that doesn't rely on canvas
 * Instead, it uses PDF.js to extract text and operator lists to make an educated guess
 * about whether the PDF contains color
 * 
 * @param pdfBuffer The PDF file as a buffer
 * @returns Object containing color detection results
 */
export async function detectColor(pdfBuffer: Buffer): Promise<ColorDetectionResult> {
  try {
    // Load the PDF document
    const loadingTask = pdfjsLib.getDocument({
      data: pdfBuffer,
      disableFontFace: true,
      ignoreErrors: true,
    });
    
    const pdf = await loadingTask.promise;
    const numPages = pdf.numPages;
    const colorPages: number[] = [];
    
    // Analyze each page
    for (let pageNum = 1; pageNum <= numPages; pageNum++) {
      try {
        const page = await pdf.getPage(pageNum);
        
        // Get the operator list - this contains drawing commands
        const opList = await page.getOperatorList();
        
        // Check for color operators (simplified approach)
        // This looks for specific PDF operators that are typically used for color
        let hasColor = false;
        
        // Check for color operators in the fnArray
        for (let i = 0; i < opList.fnArray.length; i++) {
          const op = opList.fnArray[i];
          const args = opList.argsArray[i];
          
          // Check for setFillRGBColor, setStrokeRGBColor, etc.
          // These are common operators for setting color in PDFs
          if (
            (op === pdfjsLib.OPS.setFillRGBColor || 
             op === pdfjsLib.OPS.setStrokeRGBColor) && 
            args && args.length >= 3
          ) {
            // If the RGB values are not all equal, it's likely a color
            const [r, g, b] = args;
            if (r !== g || g !== b) {
              hasColor = true;
              break;
            }
          }
          
          // Check for other color operators
          if (
            op === pdfjsLib.OPS.setFillColorSpace ||
            op === pdfjsLib.OPS.setStrokeColorSpace
          ) {
            // If it's using a color space other than DeviceGray, it might be color
            if (args && args[0] !== 'DeviceGray') {
              hasColor = true;
              break;
            }
          }
        }
        
        if (hasColor) {
          colorPages.push(pageNum);
        }
      } catch (pageError) {
        console.error(`Error analyzing page ${pageNum}:`, pageError);
        // Continue with the next page
      }
    }
    
    return {
      is_colored: colorPages.length > 0,
      color_pages: colorPages,
      total_pages: numPages
    };
  } catch (error) {
    console.error('Error analyzing PDF:', error);
    // Return a default result if analysis fails
    return {
      is_colored: false,
      color_pages: [],
      total_pages: 0
    };
  }
}
