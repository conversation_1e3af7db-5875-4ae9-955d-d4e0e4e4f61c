import { FieldMatch, FieldStatus, MatchingSummary } from '@/lib/types/invoice';

export interface FieldMatchInput {
  input_value: string | number | undefined | null;
  ocr: any;
  field: string;
  type?: 'string' | 'number' | 'date';
}

function normalizeValue(value: any, type: 'string' | 'number' | 'date', field?: string): string {
  if (value === undefined || value === null) return '';

  if (type === 'number') {
    // Handle numeric values by removing thousand separators
    let numStr = value.toString().trim();
    // Remove thousand separators (commas and dots used as thousand separators)
    // This handles formats like: 1,234,567.89, 1.234.567,89, 1,234,567, 1.234.567

    // First, identify if the last dot/comma is a decimal separator
    const lastDotIndex = numStr.lastIndexOf('.');
    const lastCommaIndex = numStr.lastIndexOf(',');

    let decimalPart = '';
    let integerPart = numStr;

    // Determine decimal separator based on position and context
    if (lastDotIndex > lastCommaIndex && lastDotIndex > 0) {
      // Last dot is likely decimal separator if it's after the last comma
      // and there are 1-3 digits after it
      const afterDot = numStr.substring(lastDotIndex + 1);
      if (afterDot.length <= 3 && /^\d+$/.test(afterDot)) {
        decimalPart = afterDot;
        integerPart = numStr.substring(0, lastDotIndex);
      }
    } else if (lastCommaIndex > lastDotIndex && lastCommaIndex > 0) {
      // Last comma is likely decimal separator if it's after the last dot
      // and there are 1-3 digits after it
      const afterComma = numStr.substring(lastCommaIndex + 1);
      if (afterComma.length <= 3 && /^\d+$/.test(afterComma)) {
        decimalPart = afterComma;
        integerPart = numStr.substring(0, lastCommaIndex);
      }
    }

    // Remove all thousand separators from integer part
    integerPart = integerPart.replace(/[,.]/g, '');

    // Reconstruct the number
    const normalizedNum = decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
    return parseFloat(normalizedNum).toString();
  }

  if (type === 'date') {
    if (typeof value === 'string') return value;
    if (value instanceof Date) return value.toISOString().split('T')[0];
    return '';
  }

  let str = value.toString().trim().toLowerCase();
  // Special normalization for tax_invoice_number
  if (field === 'tax_invoice_number') {
    str = str.replace(/[ .-]/g, '');
  }
  return str;
}

function preprocessCompanyName(name: string): string {
  let processed = name.toLowerCase();
  // Remove punctuation (replace with space)
  processed = processed.replace(/[.,/#!$%^&*;:{}=\-_`~()]/g, ' ');
  // Remove legal forms (pt, cv, tbk, group, holdings) at word boundaries
  processed = processed.replace(/\b(pt|cv|tbk|group|holdings)\b/g, ' ');
  // Remove extra spaces
  processed = processed.replace(/\s+/g, ' ').trim();
  return processed;
}

export function matchField({ input_value, ocr, field, type = 'string' }: FieldMatchInput): FieldMatch {
  const inputStr = input_value !== null && input_value !== undefined ? input_value.toString() : '';
  const normalizedInput = normalizeValue(input_value, type, field);
  const ocrValue = ocr?.ocr_value;

  // For date fields, use remote ocr_value_normalized as date normalization is complex
  // For other fields, normalize locally to ensure consistent handling of thousand separators
  let ocrNorm: string;
  if (type === 'date' && (field === 'invoice_date' || field === 'tax_invoice_date')) {
    ocrNorm = ocr?.ocr_value_normalized ?? normalizeValue(ocrValue, type, field);
  } else {
    ocrNorm = normalizeValue(ocrValue, type, field);
  }

  let status: FieldStatus = 'mismatched';
  if (!normalizedInput || ocrValue === 'N/A' || ocrNorm === 'N/A') {
    status = 'not_found';
  } else if (
    field === 'vendor_name'
      ? preprocessCompanyName(ocrNorm) === preprocessCompanyName(normalizedInput)
      : (type === 'number'
          ? Math.trunc(parseFloat(ocrNorm)) === Math.trunc(parseFloat(normalizedInput))
          : ocrNorm === normalizedInput)
  ) {
    status = 'matched';
  }

  return {
    ...ocr,
    input_value: inputStr,
    input_value_normalized: normalizedInput,
    status,
  };
}

export function matchFields(
  input: Record<string, any>,
  ocrFields: Record<string, any>,
  fieldTypes: Record<string, 'string' | 'number' | 'date'>
): { fields: Record<string, FieldMatch>; summary: MatchingSummary } {
  const fields: Record<string, FieldMatch> = {};
  let matched = 0, mismatched = 0, not_found = 0;
  for (const field in fieldTypes) {
    const match = matchField({
      input_value: input[field],
      ocr: ocrFields[field],
      field,
      type: fieldTypes[field],
    });
    fields[field] = match;
    if (match.status === 'matched') matched++;
    else if (match.status === 'mismatched') mismatched++;
    else if (match.status === 'not_found') not_found++;
  }
  const summary: MatchingSummary = {
    total_fields: Object.keys(fieldTypes).length,
    matched,
    mismatched,
    not_found,
  };
  return { fields, summary };
}
