import { FieldMatch, FieldStatus, MatchingSummary } from '@/lib/types/invoice';

export interface FieldMatchInput {
  input_value: string | number | undefined | null;
  ocr: any;
  field: string;
  type?: 'string' | 'number' | 'date';
}

function normalizeValue(value: any, type: 'string' | 'number' | 'date', field?: string): string {
  if (value === undefined || value === null) return '';
  if (type === 'number') return parseFloat(value).toString();
  if (type === 'date') {
    if (typeof value === 'string') return value;
    if (value instanceof Date) return value.toISOString().split('T')[0];
    return '';
  }
  let str = value.toString().trim().toLowerCase();
  // Special normalization for vatNumber
  if (field === 'tax_invoice_number') {
    str = str.replace(/[ .-]/g, '');
  }
  return str;
}

function preprocessCompanyName(name: string): string {
  let processed = name.toLowerCase();
  // Remove punctuation (replace with space)
  processed = processed.replace(/[.,/#!$%^&*;:{}=\-_`~()]/g, ' ');
  // Remove legal forms (pt, cv, tbk, group, holdings) at word boundaries
  processed = processed.replace(/\b(pt|cv|tbk|group|holdings)\b/g, ' ');
  // Remove extra spaces
  processed = processed.replace(/\s+/g, ' ').trim();
  return processed;
}

export function matchField({ input_value, ocr, field, type = 'string' }: FieldMatchInput): FieldMatch {
  const inputStr = input_value !== null && input_value !== undefined ? input_value.toString() : '';
  const normalizedInput = normalizeValue(input_value, type, field);
  const ocrValue = ocr?.ocr_value;
  // Always normalize ocrNorm, even if ocr_value_normalized exists
  const ocrNormRaw = ocr?.ocr_value_normalized ?? ocrValue;
  const ocrNorm = normalizeValue(ocrNormRaw, type, field);

  let status: FieldStatus = 'mismatched';
  if (!normalizedInput || ocrValue === 'N/A' || ocrNorm === 'N/A') {
    status = 'not_found';
  } else if (
    field === 'vendor_name'
      ? preprocessCompanyName(ocrNorm) === preprocessCompanyName(normalizedInput)
      : (type === 'number'
          ? Math.trunc(parseFloat(ocrNorm)) === Math.trunc(parseFloat(normalizedInput))
          : ocrNorm === normalizedInput)
  ) {
    status = 'matched';
  }

  return {
    ...ocr,
    input_value: inputStr,
    input_value_normalized: normalizedInput,
    status,
  };
}

export function matchFields(
  input: Record<string, any>,
  ocrFields: Record<string, any>,
  fieldTypes: Record<string, 'string' | 'number' | 'date'>
): { fields: Record<string, FieldMatch>; summary: MatchingSummary } {
  const fields: Record<string, FieldMatch> = {};
  let matched = 0, mismatched = 0, not_found = 0;
  for (const field in fieldTypes) {
    const match = matchField({
      input_value: input[field],
      ocr: ocrFields[field],
      field,
      type: fieldTypes[field],
    });
    fields[field] = match;
    if (match.status === 'matched') matched++;
    else if (match.status === 'mismatched') mismatched++;
    else if (match.status === 'not_found') not_found++;
  }
  const summary: MatchingSummary = {
    total_fields: Object.keys(fieldTypes).length,
    matched,
    mismatched,
    not_found,
  };
  return { fields, summary };
}
