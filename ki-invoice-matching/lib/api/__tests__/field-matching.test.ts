import { matchField } from '../field-matching';

describe('Field Matching - Number Normalization', () => {
  it('should normalize numbers with thousand separators correctly', () => {
    const testCases = [
      // Format: [input_value, ocr_value, expected_status]
      [1234567.89, '1,234,567.89', 'matched'],
      [1234567, '1,234,567', 'matched'],
      [1234567.89, '1.234.567,89', 'matched'],
      [1234567, '1.234.567', 'matched'],
      [520740, '520,740.00', 'matched'],
      [520740, '520.740,00', 'matched'],
      [4734000, '4,734,000.00', 'matched'],
      [4734000, '4.734.000,00', 'matched'],
      [0, '0.00', 'matched'],
      [0, '0,00', 'matched'],
      [100, '100', 'matched'],
      [1000, '1,000', 'matched'],
      [1000, '1.000', 'matched'],
      // Mismatched cases
      [1234567, '1,234,568', 'mismatched'],
      [520740, '520,741.00', 'mismatched'],
    ];

    testCases.forEach(([inputValue, ocrValue, expectedStatus]) => {
      const result = matchField({
        input_value: inputValue,
        ocr: { ocr_value: ocrValue },
        field: 'invoice_amount',
        type: 'number'
      });

      expect(result.status).toBe(expectedStatus);
      expect(result.input_value).toBe(inputValue.toString());
      expect(result.ocr_value).toBe(ocrValue);
    });
  });

  it('should handle edge cases in number normalization', () => {
    const testCases = [
      // Edge cases
      [1234, '1234', 'matched'], // No separators
      [1234.5, '1234.5', 'matched'], // Simple decimal
      [1234.5, '1234,5', 'matched'], // Comma as decimal separator
      [1000000, '1,000,000', 'matched'], // Multiple thousand separators
      [1000000, '1.000.000', 'matched'], // Dots as thousand separators
    ];

    testCases.forEach(([inputValue, ocrValue, expectedStatus]) => {
      const result = matchField({
        input_value: inputValue,
        ocr: { ocr_value: ocrValue },
        field: 'vat_amount',
        type: 'number'
      });

      expect(result.status).toBe(expectedStatus);
    });
  });

  it('should handle string fields without number normalization', () => {
    const result = matchField({
      input_value: 'PT. AIR LIQUIDE INDONESIA',
      ocr: { ocr_value: 'PT. AIR LIQUIDE INDONESIA' },
      field: 'vendor_name',
      type: 'string'
    });

    expect(result.status).toBe('matched');
    expect(result.input_value).toBe('PT. AIR LIQUIDE INDONESIA');
    expect(result.ocr_value).toBe('PT. AIR LIQUIDE INDONESIA');
  });

  it('should ignore remote ocr_value_normalized for numeric fields and use local normalization', () => {
    const result = matchField({
      input_value: 520740,
      ocr: {
        ocr_value: '520,740.00',
        ocr_value_normalized: 'wrong_value' // This should be ignored for numeric fields
      },
      field: 'vat_amount',
      type: 'number'
    });

    expect(result.status).toBe('matched');
    expect(result.input_value).toBe('520740');
    expect(result.ocr_value).toBe('520,740.00');
    // The ocr_value_normalized from remote should still be in the response (spread operator)
    // but it should not affect the matching logic for numeric fields
  });

  it('should use remote ocr_value_normalized for date fields', () => {
    const result = matchField({
      input_value: '2024-10-18',
      ocr: {
        ocr_value: '18/10/2024',
        ocr_value_normalized: '2024-10-18' // This should be used for date fields
      },
      field: 'invoice_date',
      type: 'date'
    });

    expect(result.status).toBe('matched');
    expect(result.input_value).toBe('2024-10-18');
    expect(result.ocr_value).toBe('18/10/2024');
  });

  it('should use remote ocr_value_normalized for tax_invoice_date fields', () => {
    const result = matchField({
      input_value: '2024-10-18',
      ocr: {
        ocr_value: '18 Oktober 2024',
        ocr_value_normalized: '2024-10-18' // This should be used for tax invoice date fields
      },
      field: 'tax_invoice_date',
      type: 'date'
    });

    expect(result.status).toBe('matched');
    expect(result.input_value).toBe('2024-10-18');
    expect(result.ocr_value).toBe('18 Oktober 2024');
  });

  it('should fall back to local normalization for date fields when ocr_value_normalized is missing', () => {
    const result = matchField({
      input_value: '2024-10-18',
      ocr: {
        ocr_value: '2024-10-18'
        // No ocr_value_normalized provided
      },
      field: 'invoice_date',
      type: 'date'
    });

    expect(result.status).toBe('matched');
    expect(result.input_value).toBe('2024-10-18');
    expect(result.ocr_value).toBe('2024-10-18');
  });
});
