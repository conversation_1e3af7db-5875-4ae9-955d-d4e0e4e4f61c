// This file configures the initialization of Sentry on the server.
// The config you add here will be used whenever the server handles a request.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from "@sentry/nextjs";

const getSampleRate = () => {
  const envRate = process.env.SENTRY_TRACES_SAMPLE_RATE;
  if (envRate) {
    return parseFloat(envRate);
  }
  return process.env.NODE_ENV === 'production' ? 0.1 : 1.0;
};

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,

  // Tag events with environment for filtering in Sentry web UI
  environment: process.env.APP_ENV || process.env.NODE_ENV || "development",

  // Sample at a higher rate in production (50% of transactions in production)
  tracesSampleRate: getSampleRate(),

  // Setting this option to true will print useful information to the console while you're setting up Sentry.
  debug: false,
});
